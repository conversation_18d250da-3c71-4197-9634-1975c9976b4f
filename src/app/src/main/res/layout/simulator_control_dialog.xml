<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Medical Data Simulator"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/closeBtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/ap_transparent"
            android:text="@string/closeButtonIcon"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/headerLayout"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Enable Simulator -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cardBackground"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Enable Simulator"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/enableSimulatorSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Auto-start on app launch"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/autoStartSwitch"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Scenario Selection -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cardBackground"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Patient Scenarios"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Patient Scenario:"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/patientScenarioSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/spinner_bg"
                        android:dropDownVerticalOffset="41dp"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:popupBackground="@color/gray" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="Alarm Scenario:"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/alarmScenarioSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/spinner_bg"
                        android:dropDownVerticalOffset="41dp"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:popupBackground="@color/gray" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Simulation Parameters -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cardBackground"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Simulation Parameters"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Update Interval -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Update Interval:"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/updateIntervalLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1000ms"
                            android:textColor="@color/lightBlue"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/updateIntervalSeekBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp" />

                    <!-- Noise Level -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Noise Level:"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/noiseLevelLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0.10"
                            android:textColor="@color/lightBlue"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/noiseLevelSeekBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp" />

                    <!-- Variation Intensity -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Variation Intensity:"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/variationIntensityLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="1.00"
                            android:textColor="@color/lightBlue"
                            android:textSize="14sp" />
                    </LinearLayout>

                    <SeekBar
                        android:id="@+id/variationIntensitySeekBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Control Buttons -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cardBackground"
                app:cardCornerRadius="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Simulation Control"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/startSimulationBtn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/green"
                            android:text="Start"
                            android:textAllCaps="false"
                            android:textColor="@color/white" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/stopSimulationBtn"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:background="@drawable/rounded_edit_text"
                            android:backgroundTint="@color/red"
                            android:text="Stop"
                            android:textAllCaps="false"
                            android:textColor="@color/white" />
                    </LinearLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/resetConfigBtn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/orange"
                        android:text="Reset to Defaults"
                        android:textAllCaps="false"
                        android:textColor="@color/white" />

                    <TextView
                        android:id="@+id/statusText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/rounded_edit_text"
                        android:backgroundTint="@color/darkGray"
                        android:padding="12dp"
                        android:text="Simulation Stopped"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
