{"patientScenarios": [{"scenarioId": "normal_adult", "name": "Normal Adult Patient", "description": "Healthy adult patient with normal vital signs", "patientInfo": {"age": 35, "gender": "M", "weight": 75.0, "height": 175.0}, "vitalSigns": {"HR": {"baseline": 75, "range": [60, 90], "unit": "bpm", "alarmLimits": {"high": 100, "low": 50, "extremeHigh": 120, "extremeLow": 40}, "variation": {"type": "sinusoidal", "amplitude": 5, "period": 300}}, "RR": {"baseline": 16, "range": [12, 20], "unit": "breaths/min", "alarmLimits": {"high": 25, "low": 8, "extremeHigh": 30, "extremeLow": 6}, "variation": {"type": "random", "amplitude": 2}}, "SpO2": {"baseline": 98, "range": [95, 100], "unit": "%", "alarmLimits": {"high": 100, "low": 90, "extremeHigh": 100, "extremeLow": 85}, "variation": {"type": "random", "amplitude": 1}}, "BP_SYS": {"baseline": 120, "range": [110, 130], "unit": "mmHg", "alarmLimits": {"high": 140, "low": 90, "extremeHigh": 160, "extremeLow": 80}, "variation": {"type": "random", "amplitude": 8}}, "BP_DIA": {"baseline": 80, "range": [70, 85], "unit": "mmHg", "alarmLimits": {"high": 90, "low": 60, "extremeHigh": 100, "extremeLow": 50}, "variation": {"type": "random", "amplitude": 5}}, "TEMP_SKIN": {"baseline": 98.6, "range": [97.5, 99.5], "unit": "°F", "alarmLimits": {"high": 100.4, "low": 96.0, "extremeHigh": 102.0, "extremeLow": 94.0}, "variation": {"type": "random", "amplitude": 0.5}}, "PI": {"baseline": 2.5, "range": [1.0, 4.0], "unit": "%", "alarmLimits": {"high": 5.0, "low": 0.5, "extremeHigh": 6.0, "extremeLow": 0.2}, "variation": {"type": "random", "amplitude": 0.3}}}}, {"scenarioId": "cardiac_patient", "name": "Cardiac Patient", "description": "Patient with cardiac conditions showing irregular heart patterns", "patientInfo": {"age": 65, "gender": "F", "weight": 68.0, "height": 162.0}, "vitalSigns": {"HR": {"baseline": 85, "range": [70, 110], "unit": "bpm", "alarmLimits": {"high": 120, "low": 50, "extremeHigh": 140, "extremeLow": 40}, "variation": {"type": "irregular", "amplitude": 15, "irregularityFactor": 0.3}}, "RR": {"baseline": 18, "range": [14, 22], "unit": "breaths/min", "alarmLimits": {"high": 25, "low": 10, "extremeHigh": 30, "extremeLow": 8}, "variation": {"type": "random", "amplitude": 3}}, "SpO2": {"baseline": 94, "range": [90, 97], "unit": "%", "alarmLimits": {"high": 100, "low": 88, "extremeHigh": 100, "extremeLow": 85}, "variation": {"type": "random", "amplitude": 2}}, "BP_SYS": {"baseline": 145, "range": [130, 160], "unit": "mmHg", "alarmLimits": {"high": 160, "low": 90, "extremeHigh": 180, "extremeLow": 80}, "variation": {"type": "random", "amplitude": 12}}, "BP_DIA": {"baseline": 95, "range": [85, 105], "unit": "mmHg", "alarmLimits": {"high": 100, "low": 60, "extremeHigh": 110, "extremeLow": 50}, "variation": {"type": "random", "amplitude": 8}}}}, {"scenarioId": "respiratory_distress", "name": "Respiratory Distress Patient", "description": "Patient experiencing respiratory difficulties", "patientInfo": {"age": 45, "gender": "M", "weight": 82.0, "height": 178.0}, "vitalSigns": {"HR": {"baseline": 105, "range": [90, 120], "unit": "bpm", "alarmLimits": {"high": 120, "low": 50, "extremeHigh": 140, "extremeLow": 40}, "variation": {"type": "random", "amplitude": 10}}, "RR": {"baseline": 28, "range": [24, 32], "unit": "breaths/min", "alarmLimits": {"high": 25, "low": 8, "extremeHigh": 35, "extremeLow": 6}, "variation": {"type": "random", "amplitude": 4}}, "SpO2": {"baseline": 88, "range": [85, 92], "unit": "%", "alarmLimits": {"high": 100, "low": 90, "extremeHigh": 100, "extremeLow": 85}, "variation": {"type": "random", "amplitude": 3}}}}, {"scenarioId": "critical_patient", "name": "Critical Care Patient", "description": "Critically ill patient with multiple system involvement", "patientInfo": {"age": 72, "gender": "F", "weight": 58.0, "height": 155.0}, "vitalSigns": {"HR": {"baseline": 125, "range": [110, 140], "unit": "bpm", "alarmLimits": {"high": 120, "low": 50, "extremeHigh": 150, "extremeLow": 40}, "variation": {"type": "irregular", "amplitude": 20, "irregularityFactor": 0.5}}, "RR": {"baseline": 32, "range": [28, 36], "unit": "breaths/min", "alarmLimits": {"high": 25, "low": 8, "extremeHigh": 40, "extremeLow": 6}, "variation": {"type": "random", "amplitude": 5}}, "SpO2": {"baseline": 82, "range": [78, 86], "unit": "%", "alarmLimits": {"high": 100, "low": 90, "extremeHigh": 100, "extremeLow": 80}, "variation": {"type": "random", "amplitude": 4}}, "BP_SYS": {"baseline": 85, "range": [75, 95], "unit": "mmHg", "alarmLimits": {"high": 140, "low": 90, "extremeHigh": 160, "extremeLow": 70}, "variation": {"type": "random", "amplitude": 15}}, "BP_DIA": {"baseline": 45, "range": [40, 55], "unit": "mmHg", "alarmLimits": {"high": 90, "low": 60, "extremeHigh": 100, "extremeLow": 40}, "variation": {"type": "random", "amplitude": 8}}}}]}