package com.spacelabs.app.simulator.generators

import android.content.Context
import android.util.Log
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.simulator.config.*
import kotlinx.coroutines.*
import kotlin.math.*
import kotlin.random.Random

/**
 * Medical data simulator engine that generates realistic medical data
 * based on JSON templates with configurable parameters for different
 * patient scenarios and time-based variations
 */
class MedicalDataSimulator(private val context: Context) {

    companion object {
        private const val TAG = "MedicalDataSimulator"
        private const val DEFAULT_UPDATE_INTERVAL_MS = 1000L // 1 second
    }

    private val dataLoader = MedicalDataLoader(context)
    private var simulatorJob: Job? = null
    private var isRunning = false
    
    // Current simulation state
    private var currentPatientScenario: PatientScenario? = null
    private var currentAlarmScenario: AlarmScenario? = null
    private var simulationStartTime = 0L
    private var lastUpdateTime = 0L
    
    // Data generation state
    private val vitalSignGenerators = mutableMapOf<String, VitalSignGenerator>()
    private val waveformGenerators = mutableMapOf<String, WaveformGenerator>()
    
    // Callbacks for data updates
    private var onVitalSignUpdate: ((String, Double, String) -> Unit)? = null
    private var onWaveformUpdate: ((String, DoubleArray, Int) -> Unit)? = null
    private var onAlarmUpdate: ((String, String, String) -> Unit)? = null

    /**
     * Start simulation with specified patient scenario
     */
    fun startSimulation(
        patientScenarioId: String,
        alarmScenarioId: String? = null,
        updateIntervalMs: Long = DEFAULT_UPDATE_INTERVAL_MS
    ): Boolean {
        return try {
            // Load scenarios
            currentPatientScenario = dataLoader.loadPatientScenario(patientScenarioId)
            currentAlarmScenario = alarmScenarioId?.let { dataLoader.loadAlarmScenario(it) }
            
            if (currentPatientScenario == null) {
                Log.e(TAG, "Failed to load patient scenario: $patientScenarioId")
                return false
            }
            
            // Initialize generators
            initializeGenerators()
            
            // Start simulation loop
            simulationStartTime = System.currentTimeMillis()
            lastUpdateTime = simulationStartTime
            isRunning = true
            
            simulatorJob = CoroutineScope(Dispatchers.Default).launch {
                while (isRunning) {
                    try {
                        updateSimulation()
                        delay(updateIntervalMs)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in simulation loop: ${e.message}")
                        LogWriter.writeExceptLog("MedicalDataSimulator", "Simulation error: ${e.stackTraceToString()}")
                    }
                }
            }
            
            Log.d(TAG, "Started simulation for patient scenario: $patientScenarioId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start simulation: ${e.message}")
            LogWriter.writeExceptLog("MedicalDataSimulator", "Start simulation error: ${e.stackTraceToString()}")
            false
        }
    }

    /**
     * Stop the simulation
     */
    fun stopSimulation() {
        isRunning = false
        simulatorJob?.cancel()
        simulatorJob = null
        vitalSignGenerators.clear()
        waveformGenerators.clear()
        Log.d(TAG, "Simulation stopped")
    }

    /**
     * Switch to a different alarm scenario during simulation
     */
    fun switchAlarmScenario(alarmScenarioId: String?) {
        currentAlarmScenario = alarmScenarioId?.let { dataLoader.loadAlarmScenario(it) }
        // Reinitialize generators with new alarm scenario
        initializeGenerators()
        Log.d(TAG, "Switched to alarm scenario: $alarmScenarioId")
    }

    /**
     * Set callback for vital sign updates
     */
    fun setOnVitalSignUpdate(callback: (paramName: String, value: Double, timestamp: String) -> Unit) {
        onVitalSignUpdate = callback
    }

    /**
     * Set callback for waveform updates
     */
    fun setOnWaveformUpdate(callback: (waveformType: String, data: DoubleArray, sampleRate: Int) -> Unit) {
        onWaveformUpdate = callback
    }

    /**
     * Set callback for alarm updates
     */
    fun setOnAlarmUpdate(callback: (paramName: String, priority: String, message: String) -> Unit) {
        onAlarmUpdate = callback
    }

    /**
     * Initialize data generators based on current scenarios
     */
    private fun initializeGenerators() {
        vitalSignGenerators.clear()
        waveformGenerators.clear()
        
        val scenario = currentPatientScenario ?: return
        
        // Initialize vital sign generators
        scenario.vitalSigns.forEach { (paramName, config) ->
            val effectiveConfig = applyAlarmOverrides(paramName, config)
            vitalSignGenerators[paramName] = VitalSignGenerator(paramName, effectiveConfig)
        }
        
        // Initialize waveform generators
        val waveformTemplates = dataLoader.loadWaveformDataTemplates()
        waveformTemplates?.waveformTemplates?.forEach { (waveformType, template) ->
            waveformGenerators[waveformType] = WaveformGenerator(waveformType, template)
        }
    }

    /**
     * Apply alarm scenario overrides to vital sign configuration
     */
    private fun applyAlarmOverrides(paramName: String, baseConfig: VitalSignConfig): VitalSignConfig {
        val alarmOverride = currentAlarmScenario?.vitalOverrides?.get(paramName)
        return if (alarmOverride != null) {
            baseConfig.copy(
                baseline = alarmOverride.baseline,
                range = alarmOverride.range
            )
        } else {
            baseConfig
        }
    }

    /**
     * Update simulation - generate new data points
     */
    private fun updateSimulation() {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - simulationStartTime
        val deltaTime = currentTime - lastUpdateTime
        
        // Generate vital signs
        vitalSignGenerators.forEach { (paramName, generator) ->
            val value = generator.generateValue(elapsedTime)
            val timestamp = TimestampUtils.getCurrentTimeAndMillis().first
            
            onVitalSignUpdate?.invoke(paramName, value, timestamp)
            
            // Check for alarms
            checkAlarms(paramName, value)
        }
        
        // Generate waveform data
        waveformGenerators.forEach { (waveformType, generator) ->
            val data = generator.generateWaveformData(elapsedTime, deltaTime.toInt())
            val sampleRate = generator.getSampleRate()
            
            onWaveformUpdate?.invoke(waveformType, data, sampleRate)
        }
        
        lastUpdateTime = currentTime
    }

    /**
     * Check if vital sign values trigger alarms
     */
    private fun checkAlarms(paramName: String, value: Double) {
        val alarmConfig = currentAlarmScenario?.parameters?.get(paramName)
        if (alarmConfig?.alarmActive == true) {
            val message = alarmConfig.message ?: "Alarm for $paramName"
            onAlarmUpdate?.invoke(paramName, alarmConfig.priority, message)
        } else {
            // Check normal alarm limits
            val config = currentPatientScenario?.vitalSigns?.get(paramName)
            config?.let { vitalConfig ->
                when {
                    value >= vitalConfig.alarmLimits.extremeHigh -> 
                        onAlarmUpdate?.invoke(paramName, "Crisis", "$paramName extremely high: $value")
                    value <= vitalConfig.alarmLimits.extremeLow -> 
                        onAlarmUpdate?.invoke(paramName, "Crisis", "$paramName extremely low: $value")
                    value >= vitalConfig.alarmLimits.high -> 
                        onAlarmUpdate?.invoke(paramName, "Warning", "$paramName high: $value")
                    value <= vitalConfig.alarmLimits.low -> 
                        onAlarmUpdate?.invoke(paramName, "Warning", "$paramName low: $value")
                }
            }
        }
    }

    /**
     * Get current simulation status
     */
    fun getSimulationStatus(): SimulationStatus {
        return SimulationStatus(
            isRunning = isRunning,
            currentPatientScenario = currentPatientScenario?.name,
            currentAlarmScenario = currentAlarmScenario?.name,
            elapsedTimeMs = if (isRunning) System.currentTimeMillis() - simulationStartTime else 0L
        )
    }

    /**
     * Get available patient scenarios
     */
    fun getAvailablePatientScenarios(): List<String> {
        return dataLoader.getAvailablePatientScenarios()
    }

    /**
     * Get available alarm scenarios
     */
    fun getAvailableAlarmScenarios(): List<String> {
        return dataLoader.getAvailableAlarmScenarios()
    }
}

/**
 * Simulation status data class
 */
data class SimulationStatus(
    val isRunning: Boolean,
    val currentPatientScenario: String?,
    val currentAlarmScenario: String?,
    val elapsedTimeMs: Long
)
