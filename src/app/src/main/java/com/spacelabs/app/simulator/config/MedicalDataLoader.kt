package com.spacelabs.app.simulator.config

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.spacelabs.app.common.LogWriter
import java.io.BufferedReader
import java.io.FileNotFoundException
import java.io.IOException
import java.io.InputStreamReader

/**
 * Utility class for loading and parsing JSON medical data files from assets
 * Provides error handling and validation for data integrity
 */
class MedicalDataLoader(private val context: Context) {

    companion object {
        private const val TAG = "MedicalDataLoader"
        private const val MEDICAL_DATA_TEMPLATES_FILE = "medical_data_templates.json"
        private const val WAVEFORM_DATA_TEMPLATES_FILE = "waveform_data_templates.json"
        private const val ALARM_SCENARIOS_FILE = "alarm_scenarios.json"
    }

    private val gson: Gson = GsonBuilder()
        .setPrettyPrinting()
        .create()

    /**
     * Load medical data templates from assets
     */
    fun loadMedicalDataTemplates(): MedicalDataTemplates? {
        return try {
            val jsonString = loadAssetFile(MEDICAL_DATA_TEMPLATES_FILE)
            gson.fromJson(jsonString, MedicalDataTemplates::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading medical data templates: ${e.message}")
            LogWriter.writeExceptLog("MedicalDataLoader", "Failed to load medical templates: ${e.stackTraceToString()}")
            null
        }
    }

    /**
     * Load waveform data templates from assets
     */
    fun loadWaveformDataTemplates(): WaveformDataTemplates? {
        return try {
            val jsonString = loadAssetFile(WAVEFORM_DATA_TEMPLATES_FILE)
            gson.fromJson(jsonString, WaveformDataTemplates::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading waveform data templates: ${e.message}")
            LogWriter.writeExceptLog("MedicalDataLoader", "Failed to load waveform templates: ${e.stackTraceToString()}")
            null
        }
    }

    /**
     * Load alarm scenarios from assets
     */
    fun loadAlarmScenarios(): AlarmScenarios? {
        return try {
            val jsonString = loadAssetFile(ALARM_SCENARIOS_FILE)
            gson.fromJson(jsonString, AlarmScenarios::class.java)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading alarm scenarios: ${e.message}")
            LogWriter.writeExceptLog("MedicalDataLoader", "Failed to load alarm scenarios: ${e.stackTraceToString()}")
            null
        }
    }

    /**
     * Load a specific patient scenario by ID
     */
    fun loadPatientScenario(scenarioId: String): PatientScenario? {
        val templates = loadMedicalDataTemplates()
        return templates?.patientScenarios?.find { it.scenarioId == scenarioId }
    }

    /**
     * Load a specific alarm scenario by ID
     */
    fun loadAlarmScenario(scenarioId: String): AlarmScenario? {
        val scenarios = loadAlarmScenarios()
        return scenarios?.alarmScenarios?.find { it.scenarioId == scenarioId }
    }

    /**
     * Get list of available patient scenario IDs
     */
    fun getAvailablePatientScenarios(): List<String> {
        return try {
            val templates = loadMedicalDataTemplates()
            templates?.patientScenarios?.map { it.scenarioId } ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available patient scenarios: ${e.message}")
            emptyList()
        }
    }

    /**
     * Get list of available alarm scenario IDs
     */
    fun getAvailableAlarmScenarios(): List<String> {
        return try {
            val scenarios = loadAlarmScenarios()
            scenarios?.alarmScenarios?.map { it.scenarioId } ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available alarm scenarios: ${e.message}")
            emptyList()
        }
    }

    /**
     * Validate JSON file structure
     */
    fun validateJsonFile(filename: String): ValidationResult {
        return try {
            val jsonString = loadAssetFile(filename)
            
            // Try to parse as generic JSON to check syntax
            gson.fromJson(jsonString, Any::class.java)
            
            // Specific validation based on file type
            when (filename) {
                MEDICAL_DATA_TEMPLATES_FILE -> {
                    val templates = gson.fromJson(jsonString, MedicalDataTemplates::class.java)
                    validateMedicalDataTemplates(templates)
                }
                WAVEFORM_DATA_TEMPLATES_FILE -> {
                    val templates = gson.fromJson(jsonString, WaveformDataTemplates::class.java)
                    validateWaveformDataTemplates(templates)
                }
                ALARM_SCENARIOS_FILE -> {
                    val scenarios = gson.fromJson(jsonString, AlarmScenarios::class.java)
                    validateAlarmScenarios(scenarios)
                }
                else -> ValidationResult(true, "File parsed successfully")
            }
        } catch (e: JsonSyntaxException) {
            ValidationResult(false, "JSON syntax error: ${e.message}")
        } catch (e: Exception) {
            ValidationResult(false, "Validation error: ${e.message}")
        }
    }

    /**
     * Load asset file as string
     */
    private fun loadAssetFile(filename: String): String {
        return try {
            BufferedReader(InputStreamReader(context.assets.open(filename))).use { reader ->
                reader.readText()
            }
        } catch (e: FileNotFoundException) {
            Log.e(TAG, "Asset file not found: $filename")
            throw IOException("Asset file not found: $filename", e)
        } catch (e: IOException) {
            Log.e(TAG, "Error reading asset file: $filename - ${e.message}")
            throw e
        }
    }

    /**
     * Validate medical data templates structure
     */
    private fun validateMedicalDataTemplates(templates: MedicalDataTemplates): ValidationResult {
        if (templates.patientScenarios.isEmpty()) {
            return ValidationResult(false, "No patient scenarios found")
        }

        for (scenario in templates.patientScenarios) {
            if (scenario.scenarioId.isBlank()) {
                return ValidationResult(false, "Patient scenario missing scenarioId")
            }
            if (scenario.vitalSigns.isEmpty()) {
                return ValidationResult(false, "Patient scenario ${scenario.scenarioId} has no vital signs")
            }
        }

        return ValidationResult(true, "Medical data templates are valid")
    }

    /**
     * Validate waveform data templates structure
     */
    private fun validateWaveformDataTemplates(templates: WaveformDataTemplates): ValidationResult {
        if (templates.waveformTemplates.isEmpty()) {
            return ValidationResult(false, "No waveform templates found")
        }

        return ValidationResult(true, "Waveform data templates are valid")
    }

    /**
     * Validate alarm scenarios structure
     */
    private fun validateAlarmScenarios(scenarios: AlarmScenarios): ValidationResult {
        if (scenarios.alarmScenarios.isEmpty()) {
            return ValidationResult(false, "No alarm scenarios found")
        }

        for (scenario in scenarios.alarmScenarios) {
            if (scenario.scenarioId.isBlank()) {
                return ValidationResult(false, "Alarm scenario missing scenarioId")
            }
        }

        return ValidationResult(true, "Alarm scenarios are valid")
    }
}

/**
 * Result of JSON validation
 */
data class ValidationResult(
    val isValid: Boolean,
    val message: String
)
