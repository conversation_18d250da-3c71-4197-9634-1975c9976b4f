package com.spacelabs.app.simulator.config

import com.google.gson.annotations.SerializedName

/**
 * Data classes for medical data templates JSON structure
 */

// Medical Data Templates
data class MedicalDataTemplates(
    @SerializedName("patientScenarios")
    val patientScenarios: List<PatientScenario>
)

data class PatientScenario(
    @SerializedName("scenarioId")
    val scenarioId: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("patientInfo")
    val patientInfo: PatientInfo,
    @SerializedName("vitalSigns")
    val vitalSigns: Map<String, VitalSignConfig>
)

data class PatientInfo(
    @SerializedName("age")
    val age: Int,
    @SerializedName("gender")
    val gender: String,
    @SerializedName("weight")
    val weight: Double,
    @SerializedName("height")
    val height: Double
)

data class VitalSignConfig(
    @SerializedName("baseline")
    val baseline: Double,
    @SerializedName("range")
    val range: List<Double>,
    @SerializedName("unit")
    val unit: String,
    @SerializedName("alarmLimits")
    val alarmLimits: AlarmLimits,
    @SerializedName("variation")
    val variation: VariationConfig
)

data class AlarmLimits(
    @SerializedName("high")
    val high: Double,
    @SerializedName("low")
    val low: Double,
    @SerializedName("extremeHigh")
    val extremeHigh: Double,
    @SerializedName("extremeLow")
    val extremeLow: Double
)

data class VariationConfig(
    @SerializedName("type")
    val type: String, // "sinusoidal", "random", "irregular"
    @SerializedName("amplitude")
    val amplitude: Double,
    @SerializedName("period")
    val period: Int? = null, // for sinusoidal
    @SerializedName("irregularityFactor")
    val irregularityFactor: Double? = null // for irregular
)

// Waveform Data Templates
data class WaveformDataTemplates(
    @SerializedName("waveformTemplates")
    val waveformTemplates: Map<String, WaveformTemplate>,
    @SerializedName("alarmPatterns")
    val alarmPatterns: Map<String, AlarmPattern>
)

data class WaveformTemplate(
    @SerializedName("sampleRate")
    val sampleRate: Int,
    @SerializedName("unit")
    val unit: String,
    @SerializedName("leads")
    val leads: Map<String, LeadConfig>? = null,
    @SerializedName("normalPattern")
    val normalPattern: NormalPattern? = null,
    @SerializedName("sampleData")
    val sampleData: Map<String, List<Double>>
)

data class LeadConfig(
    @SerializedName("normalPattern")
    val normalPattern: EcgPattern,
    @SerializedName("abnormalPatterns")
    val abnormalPatterns: Map<String, EcgPattern>
)

data class EcgPattern(
    @SerializedName("pWave")
    val pWave: WaveComponent? = null,
    @SerializedName("qrsComplex")
    val qrsComplex: WaveComponent? = null,
    @SerializedName("tWave")
    val tWave: WaveComponent? = null,
    @SerializedName("baseline")
    val baseline: Double,
    @SerializedName("heartRate")
    val heartRate: Int,
    @SerializedName("irregularity")
    val irregularity: Double? = null,
    @SerializedName("pWaveAbsent")
    val pWaveAbsent: Boolean? = null,
    @SerializedName("qrsAmplitude")
    val qrsAmplitude: Double? = null
)

data class WaveComponent(
    @SerializedName("amplitude")
    val amplitude: Double,
    @SerializedName("duration")
    val duration: Int,
    @SerializedName("shape")
    val shape: String
)

data class NormalPattern(
    @SerializedName("systolicPeak")
    val systolicPeak: Int? = null,
    @SerializedName("diastolicTrough")
    val diastolicTrough: Int? = null,
    @SerializedName("heartRate")
    val heartRate: Int? = null,
    @SerializedName("baseline")
    val baseline: Int? = null,
    @SerializedName("respiratoryRate")
    val respiratoryRate: Int? = null,
    @SerializedName("amplitude")
    val amplitude: Int? = null
)

data class AlarmPattern(
    @SerializedName("description")
    val description: String,
    @SerializedName("duration")
    val duration: Int? = null,
    @SerializedName("pattern")
    val pattern: String,
    @SerializedName("amplitude")
    val amplitude: Double? = null,
    @SerializedName("frequency")
    val frequency: Int? = null,
    @SerializedName("heartRate")
    val heartRate: Int? = null,
    @SerializedName("qrsWidth")
    val qrsWidth: Int? = null
)

// Alarm Scenarios
data class AlarmScenarios(
    @SerializedName("alarmScenarios")
    val alarmScenarios: List<AlarmScenario>,
    @SerializedName("alarmPriorities")
    val alarmPriorities: Map<String, AlarmPriorityConfig>
)

data class AlarmScenario(
    @SerializedName("scenarioId")
    val scenarioId: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("alarmState")
    val alarmState: String,
    @SerializedName("priority")
    val priority: String,
    @SerializedName("parameters")
    val parameters: Map<String, AlarmParameterConfig>,
    @SerializedName("vitalOverrides")
    val vitalOverrides: Map<String, VitalOverride>? = null,
    @SerializedName("waveformOverrides")
    val waveformOverrides: Map<String, WaveformOverride>? = null
)

data class AlarmParameterConfig(
    @SerializedName("alarmActive")
    val alarmActive: Boolean,
    @SerializedName("priority")
    val priority: String,
    @SerializedName("alarmType")
    val alarmType: String? = null,
    @SerializedName("message")
    val message: String? = null
)

data class VitalOverride(
    @SerializedName("baseline")
    val baseline: Double,
    @SerializedName("range")
    val range: List<Double>
)

data class WaveformOverride(
    @SerializedName("pattern")
    val pattern: String,
    @SerializedName("amplitude")
    val amplitude: Double
)

data class AlarmPriorityConfig(
    @SerializedName("color")
    val color: String,
    @SerializedName("soundPattern")
    val soundPattern: String,
    @SerializedName("flashRate")
    val flashRate: String
)
