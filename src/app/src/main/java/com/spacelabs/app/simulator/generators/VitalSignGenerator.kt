package com.spacelabs.app.simulator.generators

import com.spacelabs.app.simulator.config.VitalSignConfig
import kotlin.math.*
import kotlin.random.Random

/**
 * Generator for vital sign data with realistic variations
 */
class VitalSignGenerator(
    private val paramName: String,
    private val config: VitalSignConfig
) {
    
    private val random = Random.Default
    private var lastValue = config.baseline
    private var sinusoidalPhase = 0.0
    private var irregularityAccumulator = 0.0
    
    /**
     * Generate a vital sign value based on elapsed time
     */
    fun generateValue(elapsedTimeMs: Long): Double {
        val elapsedSeconds = elapsedTimeMs / 1000.0
        
        return when (config.variation.type.lowercase()) {
            "sinusoidal" -> generateSinusoidalValue(elapsedSeconds)
            "random" -> generateRandomValue()
            "irregular" -> generateIrregularValue(elapsedSeconds)
            "constant" -> config.baseline
            else -> generateRandomValue() // Default to random
        }.coerceIn(config.range[0], config.range[1])
    }
    
    /**
     * Generate sinusoidal variation around baseline
     */
    private fun generateSinusoidalValue(elapsedSeconds: Double): Double {
        val period = config.variation.period ?: 300 // Default 5 minutes
        val frequency = 2 * PI / period
        
        sinusoidalPhase = frequency * elapsedSeconds
        val variation = config.variation.amplitude * sin(sinusoidalPhase)
        
        // Add small random noise
        val noise = random.nextGaussian() * (config.variation.amplitude * 0.1)
        
        return config.baseline + variation + noise
    }
    
    /**
     * Generate random variation around baseline
     */
    private fun generateRandomValue(): Double {
        // Use Gaussian distribution for more realistic variation
        val variation = random.nextGaussian() * config.variation.amplitude
        
        // Smooth transition from last value to prevent sudden jumps
        val targetValue = config.baseline + variation
        val smoothingFactor = 0.7 // Adjust for more/less smoothing
        
        lastValue = lastValue * smoothingFactor + targetValue * (1 - smoothingFactor)
        return lastValue
    }
    
    /**
     * Generate irregular variation (e.g., for cardiac arrhythmias)
     */
    private fun generateIrregularValue(elapsedSeconds: Double): Double {
        val irregularityFactor = config.variation.irregularityFactor ?: 0.3
        
        // Base sinusoidal pattern
        val baseFrequency = 2 * PI / 60.0 // 1 minute base period
        val baseVariation = config.variation.amplitude * sin(baseFrequency * elapsedSeconds)
        
        // Add irregular component
        if (random.nextDouble() < irregularityFactor) {
            irregularityAccumulator += random.nextGaussian() * config.variation.amplitude * 0.5
        }
        
        // Decay the irregularity over time
        irregularityAccumulator *= 0.95
        
        // Add random noise
        val noise = random.nextGaussian() * (config.variation.amplitude * 0.1)
        
        return config.baseline + baseVariation + irregularityAccumulator + noise
    }
    
    /**
     * Generate parameter-specific realistic values
     */
    fun generateRealisticValue(elapsedTimeMs: Long): Double {
        return when (paramName.uppercase()) {
            "HR" -> generateHeartRate(elapsedTimeMs)
            "RR" -> generateRespiratoryRate(elapsedTimeMs)
            "SPO2" -> generateSpO2(elapsedTimeMs)
            "BP_SYS", "BP_DIA" -> generateBloodPressure(elapsedTimeMs)
            "TEMP_SKIN" -> generateTemperature(elapsedTimeMs)
            "PI" -> generatePerfusionIndex(elapsedTimeMs)
            else -> generateValue(elapsedTimeMs)
        }
    }
    
    /**
     * Generate realistic heart rate with physiological constraints
     */
    private fun generateHeartRate(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // Ensure heart rate is always positive and within physiological range
        return baseValue.coerceIn(20.0, 250.0).roundToInt().toDouble()
    }
    
    /**
     * Generate realistic respiratory rate
     */
    private fun generateRespiratoryRate(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // Respiratory rate should be positive integer
        return baseValue.coerceIn(4.0, 60.0).roundToInt().toDouble()
    }
    
    /**
     * Generate realistic SpO2 values
     */
    private fun generateSpO2(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // SpO2 is typically between 70-100%
        return baseValue.coerceIn(70.0, 100.0)
    }
    
    /**
     * Generate realistic blood pressure values
     */
    private fun generateBloodPressure(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // Blood pressure constraints
        return when (paramName.uppercase()) {
            "BP_SYS" -> baseValue.coerceIn(60.0, 250.0)
            "BP_DIA" -> baseValue.coerceIn(30.0, 150.0)
            else -> baseValue
        }
    }
    
    /**
     * Generate realistic temperature values
     */
    private fun generateTemperature(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // Temperature in Fahrenheit, typically 95-110°F
        return baseValue.coerceIn(90.0, 115.0)
    }
    
    /**
     * Generate realistic perfusion index values
     */
    private fun generatePerfusionIndex(elapsedTimeMs: Long): Double {
        val baseValue = generateValue(elapsedTimeMs)
        
        // PI is typically 0.1-10%
        return baseValue.coerceIn(0.1, 10.0)
    }
    
    /**
     * Get current configuration
     */
    fun getConfig(): VitalSignConfig = config
    
    /**
     * Reset generator state
     */
    fun reset() {
        lastValue = config.baseline
        sinusoidalPhase = 0.0
        irregularityAccumulator = 0.0
    }
    
    /**
     * Check if current value would trigger an alarm
     */
    fun checkAlarmStatus(value: Double): AlarmStatus {
        return when {
            value >= config.alarmLimits.extremeHigh -> AlarmStatus.EXTREME_HIGH
            value <= config.alarmLimits.extremeLow -> AlarmStatus.EXTREME_LOW
            value >= config.alarmLimits.high -> AlarmStatus.HIGH
            value <= config.alarmLimits.low -> AlarmStatus.LOW
            else -> AlarmStatus.NORMAL
        }
    }
}

/**
 * Alarm status enumeration
 */
enum class AlarmStatus {
    NORMAL,
    LOW,
    HIGH,
    EXTREME_LOW,
    EXTREME_HIGH
}
