package com.spacelabs.app.simulator

import android.content.Context
import android.util.Log
import com.spacelabs.app.R
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.simulator.config.SimulatorConfigManager
import com.spacelabs.app.simulator.sensors.SimulatedSensorManager
import com.spacelabs.app.simulator.ui.SimulatorControlDialog

/**
 * Integration helper for the medical data simulator
 * Provides easy access to simulator functionality from the main app
 */
class SimulatorIntegration(private val context: Context) {

    companion object {
        private const val TAG = "SimulatorIntegration"
        
        @Volatile
        private var INSTANCE: SimulatorIntegration? = null
        
        fun getInstance(context: Context): SimulatorIntegration {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SimulatorIntegration(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val configManager = SimulatorConfigManager(context)
    private val simulatedSensorManager = SimulatedSensorManager(context)
    
    /**
     * Initialize the simulator integration
     * Call this from MainActivity onCreate or Application class
     */
    fun initialize() {
        try {
            Log.d(TAG, "Initializing medical data simulator integration")
            
            // Validate JSON data files
            validateDataFiles()
            
            // Auto-start simulation if enabled
            if (configManager.isAutoStartEnabled() && configManager.isSimulatorEnabled()) {
                autoStartSimulation()
            }
            
            Log.d(TAG, "Simulator integration initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing simulator integration: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Initialization error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Show the simulator control dialog
     */
    fun showSimulatorControlDialog() {
        try {
            val dialog = SimulatorControlDialog(context, simulatedSensorManager)
            dialog.createWrapContentDialogs(R.layout.simulator_control_dialog)
        } catch (e: Exception) {
            Log.e(TAG, "Error showing simulator control dialog: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Dialog error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Start simulation with default or saved settings
     */
    fun startSimulation(): Boolean {
        return try {
            if (!configManager.isSimulatorEnabled()) {
                Log.w(TAG, "Simulator is not enabled")
                return false
            }

            val patientScenario = configManager.getCurrentPatientScenario()
            if (patientScenario == null) {
                Log.w(TAG, "No patient scenario selected")
                return false
            }

            val alarmScenario = configManager.getCurrentAlarmScenario()
            val updateInterval = configManager.getUpdateInterval()

            simulatedSensorManager.startSimulation(patientScenario, alarmScenario, updateInterval)
        } catch (e: Exception) {
            Log.e(TAG, "Error starting simulation: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Start simulation error: ${e.stackTraceToString()}")
            false
        }
    }

    /**
     * Stop the simulation
     */
    fun stopSimulation() {
        try {
            simulatedSensorManager.stopSimulation()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping simulation: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Stop simulation error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Check if simulation is currently running
     */
    fun isSimulationRunning(): Boolean {
        return try {
            simulatedSensorManager.getSimulationStatus().isActive
        } catch (e: Exception) {
            Log.e(TAG, "Error checking simulation status: ${e.message}")
            false
        }
    }

    /**
     * Get current simulation status
     */
    fun getSimulationStatus(): String {
        return try {
            val status = simulatedSensorManager.getSimulationStatus()
            if (status.isActive) {
                "Running: ${status.currentPatientScenario ?: "Unknown"}"
            } else {
                "Stopped"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting simulation status: ${e.message}")
            "Error"
        }
    }

    /**
     * Switch to a different alarm scenario during simulation
     */
    fun switchAlarmScenario(alarmScenarioId: String?) {
        try {
            simulatedSensorManager.switchAlarmScenario(alarmScenarioId)
            configManager.setCurrentAlarmScenario(alarmScenarioId)
        } catch (e: Exception) {
            Log.e(TAG, "Error switching alarm scenario: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Switch alarm scenario error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Get available patient scenarios
     */
    fun getAvailablePatientScenarios(): List<Pair<String, String>> {
        return try {
            configManager.getAvailablePatientScenariosWithNames()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting patient scenarios: ${e.message}")
            emptyList()
        }
    }

    /**
     * Get available alarm scenarios
     */
    fun getAvailableAlarmScenarios(): List<Pair<String, String>> {
        return try {
            configManager.getAvailableAlarmScenariosWithNames()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting alarm scenarios: ${e.message}")
            emptyList()
        }
    }

    /**
     * Enable or disable the simulator
     */
    fun setSimulatorEnabled(enabled: Boolean) {
        configManager.setSimulatorEnabled(enabled)
        if (!enabled && isSimulationRunning()) {
            stopSimulation()
        }
    }

    /**
     * Check if simulator is enabled
     */
    fun isSimulatorEnabled(): Boolean {
        return configManager.isSimulatorEnabled()
    }

    /**
     * Get configuration manager for advanced settings
     */
    fun getConfigManager(): SimulatorConfigManager = configManager

    /**
     * Get simulated sensor manager for advanced control
     */
    fun getSimulatedSensorManager(): SimulatedSensorManager = simulatedSensorManager

    /**
     * Validate data files on initialization
     */
    private fun validateDataFiles() {
        try {
            val dataLoader = com.spacelabs.app.simulator.config.MedicalDataLoader(context)
            
            // Validate medical data templates
            val medicalValidation = dataLoader.validateJsonFile("medical_data_templates.json")
            if (!medicalValidation.isValid) {
                Log.w(TAG, "Medical data templates validation failed: ${medicalValidation.message}")
            }
            
            // Validate waveform data templates
            val waveformValidation = dataLoader.validateJsonFile("waveform_data_templates.json")
            if (!waveformValidation.isValid) {
                Log.w(TAG, "Waveform data templates validation failed: ${waveformValidation.message}")
            }
            
            // Validate alarm scenarios
            val alarmValidation = dataLoader.validateJsonFile("alarm_scenarios.json")
            if (!alarmValidation.isValid) {
                Log.w(TAG, "Alarm scenarios validation failed: ${alarmValidation.message}")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error validating data files: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Data validation error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Auto-start simulation with saved settings
     */
    private fun autoStartSimulation() {
        try {
            Log.d(TAG, "Auto-starting simulation")
            val success = startSimulation()
            if (success) {
                Log.d(TAG, "Auto-start simulation successful")
            } else {
                Log.w(TAG, "Auto-start simulation failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in auto-start simulation: ${e.message}")
            LogWriter.writeExceptLog("SimulatorIntegration", "Auto-start error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Cleanup resources when app is destroyed
     */
    fun cleanup() {
        try {
            stopSimulation()
            Log.d(TAG, "Simulator integration cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup: ${e.message}")
        }
    }

    /**
     * Quick access methods for common scenarios
     */
    object QuickAccess {
        
        fun startNormalPatient(context: Context): Boolean {
            return getInstance(context).apply {
                configManager.setCurrentPatientScenario("normal_adult")
                configManager.setCurrentAlarmScenario(null)
            }.startSimulation()
        }
        
        fun startCardiacPatient(context: Context): Boolean {
            return getInstance(context).apply {
                configManager.setCurrentPatientScenario("cardiac_patient")
                configManager.setCurrentAlarmScenario(null)
            }.startSimulation()
        }
        
        fun startCriticalPatient(context: Context): Boolean {
            return getInstance(context).apply {
                configManager.setCurrentPatientScenario("critical_patient")
                configManager.setCurrentAlarmScenario("multiple_alarms")
            }.startSimulation()
        }
        
        fun triggerBradycardiaAlarm(context: Context) {
            getInstance(context).switchAlarmScenario("bradycardia_warning")
        }
        
        fun triggerTachycardiaAlarm(context: Context) {
            getInstance(context).switchAlarmScenario("tachycardia_crisis")
        }
        
        fun clearAlarms(context: Context) {
            getInstance(context).switchAlarmScenario(null)
        }
    }
}
