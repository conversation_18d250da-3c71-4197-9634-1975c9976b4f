package com.spacelabs.app.simulator.sensors

import android.content.Context
import android.util.Log
import com.sibelhealth.bluetooth.sensorservice.datastream.StreamDataType
import com.sibelhealth.core.sensor.Sensor
import com.sibelhealth.core.sensor.SensorType
import com.spacelabs.app.common.CommonDataArea
import com.spacelabs.app.common.CommonEventHandler
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.common.TimestampUtils
import com.spacelabs.app.dataManager.MeasurementDataManager
import com.spacelabs.app.interfaces.IOEventCallback
import com.spacelabs.app.interfaces.UiEventCallback
import com.spacelabs.app.simulator.generators.MedicalDataSimulator
import kotlinx.coroutines.*

/**
 * Simulated sensor manager that integrates the JSON-based medical data simulator
 * with the existing measurement data system, replacing database-dependent approaches
 */
class SimulatedSensorManager(private val context: Context) {

    companion object {
        private const val TAG = "SimulatedSensorManager"
        private const val SIMULATED_CHEST_SENSOR_ID = 9999
        private const val SIMULATED_LIMB_SENSOR_ID = 9998
        private const val SIMULATED_BP_SENSOR_ID = 9997
    }

    private val medicalDataSimulator = MedicalDataSimulator(context)
    private val measurementDataManager = MeasurementDataManager()
    
    // Simulated sensors
    private val simulatedChestSensor = createSimulatedSensor(SensorType.CHEST, "SimulatedChest")
    private val simulatedLimbSensor = createSimulatedSensor(SensorType.LIMB, "SimulatedLimb")
    private val simulatedBpSensor = createSimulatedSensor(SensorType.BP, "SimulatedBP")
    
    private var isSimulationActive = false
    private var currentPatientScenario: String? = null
    private var currentAlarmScenario: String? = null

    /**
     * Start medical data simulation
     */
    fun startSimulation(
        patientScenarioId: String,
        alarmScenarioId: String? = null,
        updateIntervalMs: Long = 1000L
    ): Boolean {
        return try {
            // Setup simulated sensors
            setupSimulatedSensors()
            
            // Configure simulator callbacks
            setupSimulatorCallbacks()
            
            // Start the medical data simulator
            val success = medicalDataSimulator.startSimulation(
                patientScenarioId, 
                alarmScenarioId, 
                updateIntervalMs
            )
            
            if (success) {
                isSimulationActive = true
                currentPatientScenario = patientScenarioId
                currentAlarmScenario = alarmScenarioId
                
                // Notify UI that simulation started
                CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorConnected, null)
                
                Log.d(TAG, "Simulation started successfully")
            } else {
                Log.e(TAG, "Failed to start simulation")
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error starting simulation: ${e.message}")
            LogWriter.writeExceptLog("SimulatedSensorManager", "Start simulation error: ${e.stackTraceToString()}")
            false
        }
    }

    /**
     * Stop medical data simulation
     */
    fun stopSimulation() {
        try {
            medicalDataSimulator.stopSimulation()
            isSimulationActive = false
            currentPatientScenario = null
            currentAlarmScenario = null
            
            // Notify UI that simulation stopped
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.SensorDisconnected, null)
            
            Log.d(TAG, "Simulation stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping simulation: ${e.message}")
            LogWriter.writeExceptLog("SimulatedSensorManager", "Stop simulation error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Switch to different alarm scenario during simulation
     */
    fun switchAlarmScenario(alarmScenarioId: String?) {
        if (isSimulationActive) {
            medicalDataSimulator.switchAlarmScenario(alarmScenarioId)
            currentAlarmScenario = alarmScenarioId
            Log.d(TAG, "Switched to alarm scenario: $alarmScenarioId")
        }
    }

    /**
     * Setup simulated sensors in the system
     */
    private fun setupSimulatedSensors() {
        // Set simulated sensor IDs in CommonDataArea
        CommonDataArea.currentChestSensorId = SIMULATED_CHEST_SENSOR_ID
        CommonDataArea.currentLimbSensorId = SIMULATED_LIMB_SENSOR_ID
        CommonDataArea.currentBpSensorId = SIMULATED_BP_SENSOR_ID
        
        Log.d(TAG, "Simulated sensors setup complete")
    }

    /**
     * Configure callbacks for the medical data simulator
     */
    private fun setupSimulatorCallbacks() {
        // Vital sign updates
        medicalDataSimulator.setOnVitalSignUpdate { paramName, value, timestamp ->
            handleVitalSignUpdate(paramName, value, timestamp)
        }
        
        // Waveform updates
        medicalDataSimulator.setOnWaveformUpdate { waveformType, data, sampleRate ->
            handleWaveformUpdate(waveformType, data, sampleRate)
        }
        
        // Alarm updates
        medicalDataSimulator.setOnAlarmUpdate { paramName, priority, message ->
            handleAlarmUpdate(paramName, priority, message)
        }
    }

    /**
     * Handle vital sign updates from simulator
     */
    private fun handleVitalSignUpdate(paramName: String, value: Double, timestamp: String) {
        try {
            val sensor = getSensorForParameter(paramName)
            val timestampAndMillis = Pair(timestamp, TimestampUtils.timestampToMillis(timestamp))
            
            // Update CommonDataArea values
            updateCommonDataAreaValues(paramName, value)
            
            // Insert into database through MeasurementDataManager
            measurementDataManager.insertVitalsToDB(sensor, value, paramName, timestampAndMillis)
            
            // Post UI update event
            CommonEventHandler.postUiEvent(UiEventCallback.UiEventType.VitalSignUpdate, Pair(paramName, value))
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling vital sign update: ${e.message}")
            LogWriter.writeExceptLog("SimulatedSensorManager", "Vital sign update error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Handle waveform updates from simulator
     */
    private fun handleWaveformUpdate(waveformType: String, data: DoubleArray, sampleRate: Int) {
        try {
            val sensor = getSensorForWaveform(waveformType)
            val timestamp = TimestampUtils.getCurrentTimeAndMillis()
            
            // Convert double array to byte array for storage
            val byteArray = convertDoubleArrayToByteArray(data)
            
            // Insert waveform data into database
            measurementDataManager.insertByteStreamToDB2(sensor, byteArray, waveformType, timestamp)
            
            // Update chart data sources
            updateChartDataSources(waveformType, data)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling waveform update: ${e.message}")
            LogWriter.writeExceptLog("SimulatedSensorManager", "Waveform update error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Handle alarm updates from simulator
     */
    private fun handleAlarmUpdate(paramName: String, priority: String, message: String) {
        try {
            // Post alarm event
            CommonEventHandler.postUiEvent(
                UiEventCallback.UiEventType.AlarmTriggered, 
                Triple(paramName, priority, message)
            )
            
            Log.d(TAG, "Alarm: $paramName - $priority - $message")
        } catch (e: Exception) {
            Log.e(TAG, "Error handling alarm update: ${e.message}")
            LogWriter.writeExceptLog("SimulatedSensorManager", "Alarm update error: ${e.stackTraceToString()}")
        }
    }

    /**
     * Update CommonDataArea values based on parameter name
     */
    private fun updateCommonDataAreaValues(paramName: String, value: Double) {
        when (paramName.uppercase()) {
            "HR" -> CommonDataArea.HR = value.toInt()
            "RR" -> CommonDataArea.RR = value.toInt()
            "SPO2" -> CommonDataArea.spo2 = value
            "PI" -> CommonDataArea.PI = value
            "PR" -> CommonDataArea.PR = value
            "TEMP_SKIN" -> CommonDataArea.skinTemperature = value.toFloat()
            // Add other parameters as needed
        }
    }

    /**
     * Get appropriate sensor for parameter
     */
    private fun getSensorForParameter(paramName: String): Sensor {
        return when (paramName.uppercase()) {
            "HR", "RR", "SPO2", "PI", "ECG", "RESP", "PPG" -> simulatedChestSensor
            "BP_SYS", "BP_DIA", "BP" -> simulatedBpSensor
            "TEMP_SKIN", "ANGLE", "BODY_POSITION" -> simulatedLimbSensor
            else -> simulatedChestSensor // Default to chest sensor
        }
    }

    /**
     * Get appropriate sensor for waveform type
     */
    private fun getSensorForWaveform(waveformType: String): Sensor {
        return when (waveformType.uppercase()) {
            "ECG", "RESP", "PPG" -> simulatedChestSensor
            "BP" -> simulatedBpSensor
            else -> simulatedChestSensor
        }
    }

    /**
     * Update chart data sources with new waveform data
     */
    private fun updateChartDataSources(waveformType: String, data: DoubleArray) {
        // Update the appropriate chart data source
        when (waveformType.uppercase()) {
            "ECG" -> updateECGDataSource(data)
            "PPG" -> updatePPGDataSource(data)
            "RESP" -> updateRespDataSource(data)
        }
    }

    /**
     * Update ECG chart data source
     */
    private fun updateECGDataSource(data: DoubleArray) {
        // Implementation depends on how ECG data source is accessed
        // This would integrate with the existing ECGDataSource class
    }

    /**
     * Update PPG chart data source
     */
    private fun updatePPGDataSource(data: DoubleArray) {
        // Implementation depends on how PPG data source is accessed
        // This would integrate with the existing PPGDataSource class
    }

    /**
     * Update respiratory chart data source
     */
    private fun updateRespDataSource(data: DoubleArray) {
        // Implementation depends on how respiratory data source is accessed
    }

    /**
     * Convert double array to byte array for database storage
     */
    private fun convertDoubleArrayToByteArray(data: DoubleArray): ByteArray {
        // Simple conversion - in production, consider more efficient serialization
        return data.joinToString(",").toByteArray()
    }

    /**
     * Create a simulated sensor
     */
    private fun createSimulatedSensor(sensorType: SensorType, name: String): Sensor {
        return object : Sensor {
            override val sensorType: SensorType = sensorType
            override val name: String = name
            override val address: String = "SIMULATED_${sensorType.name}"
            override val isConnected: Boolean = true
        }
    }

    /**
     * Get simulation status
     */
    fun getSimulationStatus(): SimulationStatus {
        return SimulationStatus(
            isActive = isSimulationActive,
            currentPatientScenario = currentPatientScenario,
            currentAlarmScenario = currentAlarmScenario,
            simulatorStatus = medicalDataSimulator.getSimulationStatus()
        )
    }

    /**
     * Get available scenarios
     */
    fun getAvailablePatientScenarios(): List<String> = medicalDataSimulator.getAvailablePatientScenarios()
    fun getAvailableAlarmScenarios(): List<String> = medicalDataSimulator.getAvailableAlarmScenarios()
}

/**
 * Simulation status data class
 */
data class SimulationStatus(
    val isActive: Boolean,
    val currentPatientScenario: String?,
    val currentAlarmScenario: String?,
    val simulatorStatus: com.spacelabs.app.simulator.generators.SimulationStatus
)
