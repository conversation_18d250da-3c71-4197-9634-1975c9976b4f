package com.spacelabs.app.simulator.ui

import android.app.Dialog
import android.content.Context
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.SwitchCompat
import com.spacelabs.app.R
import com.spacelabs.app.common.LogWriter
import com.spacelabs.app.simulator.config.SimulatorConfigManager
import com.spacelabs.app.simulator.sensors.SimulatedSensorManager
import com.spacelabs.app.ui.dialogs.BaseDialogController

/**
 * Dialog controller for managing medical data simulator settings and controls
 */
class SimulatorControlDialog(
    private val context: Context,
    private val simulatedSensorManager: SimulatedSensorManager
) : BaseDialogController(context) {

    companion object {
        private const val TAG = "SimulatorControlDialog"
    }

    private val configManager = SimulatorConfigManager(context)

    // UI Components
    private lateinit var enableSimulatorSwitch: SwitchCompat
    private lateinit var autoStartSwitch: SwitchCompat
    private lateinit var patientScenarioSpinner: Spinner
    private lateinit var alarmScenarioSpinner: Spinner
    private lateinit var updateIntervalSeekBar: SeekBar
    private lateinit var updateIntervalLabel: TextView
    private lateinit var noiseLevelSeekBar: SeekBar
    private lateinit var noiseLevelLabel: TextView
    private lateinit var variationIntensitySeekBar: SeekBar
    private lateinit var variationIntensityLabel: TextView
    private lateinit var startSimulationBtn: AppCompatButton
    private lateinit var stopSimulationBtn: AppCompatButton
    private lateinit var resetConfigBtn: AppCompatButton
    private lateinit var statusText: TextView
    private lateinit var closeBtn: Button

    // Data
    private var patientScenarios: List<Pair<String, String>> = emptyList()
    private var alarmScenarios: List<Pair<String, String>> = emptyList()

    override fun initUiComponents(view: View) {
        enableSimulatorSwitch = view.findViewById(R.id.enableSimulatorSwitch)
        autoStartSwitch = view.findViewById(R.id.autoStartSwitch)
        patientScenarioSpinner = view.findViewById(R.id.patientScenarioSpinner)
        alarmScenarioSpinner = view.findViewById(R.id.alarmScenarioSpinner)
        updateIntervalSeekBar = view.findViewById(R.id.updateIntervalSeekBar)
        updateIntervalLabel = view.findViewById(R.id.updateIntervalLabel)
        noiseLevelSeekBar = view.findViewById(R.id.noiseLevelSeekBar)
        noiseLevelLabel = view.findViewById(R.id.noiseLevelLabel)
        variationIntensitySeekBar = view.findViewById(R.id.variationIntensitySeekBar)
        variationIntensityLabel = view.findViewById(R.id.variationIntensityLabel)
        startSimulationBtn = view.findViewById(R.id.startSimulationBtn)
        stopSimulationBtn = view.findViewById(R.id.stopSimulationBtn)
        resetConfigBtn = view.findViewById(R.id.resetConfigBtn)
        statusText = view.findViewById(R.id.statusText)
        closeBtn = view.findViewById(R.id.closeBtn)
    }

    override fun postUiInitActions(context: Context) {
        loadScenarios()
        setupSpinners()
        setupSeekBars()
        loadCurrentConfiguration()
        updateSimulationStatus()
    }

    override fun uiActionListeners(context: Context, dialog: Dialog) {
        enableSimulatorSwitch.setOnCheckedChangeListener { _, isChecked ->
            configManager.setSimulatorEnabled(isChecked)
            updateUIState()
        }

        autoStartSwitch.setOnCheckedChangeListener { _, isChecked ->
            configManager.setAutoStartEnabled(isChecked)
        }

        patientScenarioSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0 && patientScenarios.isNotEmpty()) {
                    val selectedScenario = patientScenarios[position - 1].first
                    configManager.setCurrentPatientScenario(selectedScenario)
                } else {
                    configManager.setCurrentPatientScenario(null)
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        alarmScenarioSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0 && alarmScenarios.isNotEmpty()) {
                    val selectedScenario = alarmScenarios[position - 1].first
                    configManager.setCurrentAlarmScenario(selectedScenario)
                } else {
                    configManager.setCurrentAlarmScenario(null)
                }
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        updateIntervalSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val intervalMs = (progress + 1) * 100L // 100ms to 10000ms
                updateIntervalLabel.text = "${intervalMs}ms"
                if (fromUser) {
                    configManager.setUpdateInterval(intervalMs)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        noiseLevelSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val noiseLevel = progress / 100.0f
                noiseLevelLabel.text = String.format("%.2f", noiseLevel)
                if (fromUser) {
                    configManager.setNoiseLevel(noiseLevel)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        variationIntensitySeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val intensity = progress / 50.0f // 0.0 to 2.0
                variationIntensityLabel.text = String.format("%.2f", intensity)
                if (fromUser) {
                    configManager.setVariationIntensity(intensity)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        startSimulationBtn.setOnClickListener {
            startSimulation()
        }

        stopSimulationBtn.setOnClickListener {
            stopSimulation()
        }

        resetConfigBtn.setOnClickListener {
            resetConfiguration()
        }

        closeBtn.setOnClickListener {
            closeDialog(dialog)
        }
    }

    private fun loadScenarios() {
        try {
            patientScenarios = configManager.getAvailablePatientScenariosWithNames()
            alarmScenarios = configManager.getAvailableAlarmScenariosWithNames()
        } catch (e: Exception) {
            Log.e(TAG, "Error loading scenarios: ${e.message}")
            LogWriter.writeExceptLog("SimulatorControlDialog", "Error loading scenarios: ${e.stackTraceToString()}")
        }
    }

    private fun setupSpinners() {
        // Patient scenario spinner
        val patientScenarioNames = listOf("None") + patientScenarios.map { it.second }
        val patientAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, patientScenarioNames)
        patientAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        patientScenarioSpinner.adapter = patientAdapter

        // Alarm scenario spinner
        val alarmScenarioNames = listOf("None") + alarmScenarios.map { it.second }
        val alarmAdapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, alarmScenarioNames)
        alarmAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        alarmScenarioSpinner.adapter = alarmAdapter
    }

    private fun setupSeekBars() {
        // Update interval: 100ms to 10000ms
        updateIntervalSeekBar.max = 99 // 0-99, representing 100ms to 10000ms

        // Noise level: 0.0 to 1.0
        noiseLevelSeekBar.max = 100

        // Variation intensity: 0.0 to 2.0
        variationIntensitySeekBar.max = 100
    }

    private fun loadCurrentConfiguration() {
        val config = configManager.getConfigurationSummary()

        enableSimulatorSwitch.isChecked = config.isEnabled
        autoStartSwitch.isChecked = config.autoStart

        // Set patient scenario spinner
        config.patientScenario?.let { scenarioId ->
            val index = patientScenarios.indexOfFirst { it.first == scenarioId }
            if (index >= 0) {
                patientScenarioSpinner.setSelection(index + 1) // +1 for "None" option
            }
        }

        // Set alarm scenario spinner
        config.alarmScenario?.let { scenarioId ->
            val index = alarmScenarios.indexOfFirst { it.first == scenarioId }
            if (index >= 0) {
                alarmScenarioSpinner.setSelection(index + 1) // +1 for "None" option
            }
        }

        // Set seek bars
        updateIntervalSeekBar.progress = ((config.updateIntervalMs / 100) - 1).toInt()
        updateIntervalLabel.text = "${config.updateIntervalMs}ms"

        noiseLevelSeekBar.progress = (config.noiseLevel * 100).toInt()
        noiseLevelLabel.text = String.format("%.2f", config.noiseLevel)

        variationIntensitySeekBar.progress = (config.variationIntensity * 50).toInt()
        variationIntensityLabel.text = String.format("%.2f", config.variationIntensity)

        updateUIState()
    }

    private fun updateUIState() {
        val isEnabled = enableSimulatorSwitch.isChecked
        val simulationStatus = simulatedSensorManager.getSimulationStatus()

        // Enable/disable controls based on simulator state
        patientScenarioSpinner.isEnabled = isEnabled && !simulationStatus.isActive
        alarmScenarioSpinner.isEnabled = isEnabled
        updateIntervalSeekBar.isEnabled = isEnabled && !simulationStatus.isActive
        noiseLevelSeekBar.isEnabled = isEnabled && !simulationStatus.isActive
        variationIntensitySeekBar.isEnabled = isEnabled && !simulationStatus.isActive

        startSimulationBtn.isEnabled = isEnabled && !simulationStatus.isActive
        stopSimulationBtn.isEnabled = isEnabled && simulationStatus.isActive
    }

    private fun updateSimulationStatus() {
        val status = simulatedSensorManager.getSimulationStatus()
        val statusMessage = if (status.isActive) {
            "Simulation Active\nPatient: ${status.currentPatientScenario ?: "None"}\nAlarm: ${status.currentAlarmScenario ?: "None"}"
        } else {
            "Simulation Stopped"
        }
        statusText.text = statusMessage
        updateUIState()
    }

    private fun startSimulation() {
        try {
            val patientScenario = configManager.getCurrentPatientScenario()
            if (patientScenario == null) {
                Toast.makeText(context, "Please select a patient scenario", Toast.LENGTH_SHORT).show()
                return
            }

            val alarmScenario = configManager.getCurrentAlarmScenario()
            val updateInterval = configManager.getUpdateInterval()

            val success = simulatedSensorManager.startSimulation(patientScenario, alarmScenario, updateInterval)
            if (success) {
                Toast.makeText(context, "Simulation started", Toast.LENGTH_SHORT).show()
                updateSimulationStatus()
            } else {
                Toast.makeText(context, "Failed to start simulation", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting simulation: ${e.message}")
            Toast.makeText(context, "Error starting simulation: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun stopSimulation() {
        try {
            simulatedSensorManager.stopSimulation()
            Toast.makeText(context, "Simulation stopped", Toast.LENGTH_SHORT).show()
            updateSimulationStatus()
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping simulation: ${e.message}")
            Toast.makeText(context, "Error stopping simulation: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun resetConfiguration() {
        configManager.resetToDefaults()
        loadCurrentConfiguration()
        Toast.makeText(context, "Configuration reset to defaults", Toast.LENGTH_SHORT).show()
    }
}
