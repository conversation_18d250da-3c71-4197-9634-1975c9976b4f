package com.spacelabs.app

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context

class ApplicationClass: Application() {
    private var mContext: Context? = null

    companion object{
        @SuppressLint("StaticFieldLeak")
        var applicationInstance: ApplicationClass? = null
        fun getInstance(): ApplicationClass? {
            return applicationInstance
        }
    }

    override fun onCreate() {
        super.onCreate()
        mContext = applicationContext
        applicationInstance = this
    }

    override fun getApplicationContext(): Context {
        return super.getApplicationContext()
    }
}