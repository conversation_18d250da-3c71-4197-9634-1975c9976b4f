package com.spacelabs.app.simulator.generators

import com.spacelabs.app.simulator.config.WaveformTemplate
import kotlin.math.*
import kotlin.random.Random

/**
 * Generator for waveform data (ECG, PPG, RESP) with realistic patterns
 */
class WaveformGenerator(
    private val waveformType: String,
    private val template: WaveformTemplate
) {
    
    private val random = Random.Default
    private var sampleIndex = 0
    private var heartBeatPhase = 0.0
    private var respiratoryPhase = 0.0
    private var lastSampleTime = 0L
    
    // Get sample data for the waveform type
    private val baseSampleData = template.sampleData.values.firstOrNull() ?: emptyList()
    
    /**
     * Generate waveform data for the specified time period
     */
    fun generateWaveformData(elapsedTimeMs: Long, deltaTimeMs: Int): DoubleArray {
        val samplesNeeded = (template.sampleRate * deltaTimeMs / 1000.0).toInt()
        
        return when (waveformType.uppercase()) {
            "ECG" -> generateECGData(elapsedTimeMs, samplesNeeded)
            "PPG" -> generatePPGData(elapsedTimeMs, samplesNeeded)
            "RESP" -> generateRESPData(elapsedTimeMs, samplesNeeded)
            else -> generateGenericWaveform(samplesNeeded)
        }
    }
    
    /**
     * Generate ECG waveform data
     */
    private fun generateECGData(elapsedTimeMs: Long, samplesNeeded: Int): DoubleArray {
        val data = DoubleArray(samplesNeeded)
        val heartRate = template.leads?.get("Lead_II")?.normalPattern?.heartRate ?: 75
        val beatsPerSecond = heartRate / 60.0
        val samplesPerBeat = template.sampleRate / beatsPerSecond
        
        for (i in 0 until samplesNeeded) {
            val beatPosition = (sampleIndex % samplesPerBeat.toInt()) / samplesPerBeat
            data[i] = generateECGSample(beatPosition, elapsedTimeMs)
            sampleIndex++
        }
        
        return data
    }
    
    /**
     * Generate a single ECG sample based on beat position
     */
    private fun generateECGSample(beatPosition: Double, elapsedTimeMs: Long): Double {
        val leadConfig = template.leads?.get("Lead_II")?.normalPattern
        val baseline = leadConfig?.baseline ?: 0.0
        val qrsAmplitude = leadConfig?.qrsAmplitude ?: 1.2
        
        // Generate ECG complex based on position in cardiac cycle
        val ecgValue = when {
            // P wave (0.0 - 0.15)
            beatPosition < 0.15 -> {
                val pPhase = beatPosition / 0.15 * 2 * PI
                baseline + 0.25 * sin(pPhase)
            }
            // PR segment (0.15 - 0.25)
            beatPosition < 0.25 -> baseline
            // QRS complex (0.25 - 0.35)
            beatPosition < 0.35 -> {
                val qrsPhase = (beatPosition - 0.25) / 0.1
                when {
                    qrsPhase < 0.3 -> baseline - 0.3 * qrsAmplitude // Q wave
                    qrsPhase < 0.7 -> baseline + qrsAmplitude * sin((qrsPhase - 0.3) / 0.4 * PI) // R wave
                    else -> baseline - 0.2 * qrsAmplitude // S wave
                }
            }
            // ST segment (0.35 - 0.5)
            beatPosition < 0.5 -> baseline
            // T wave (0.5 - 0.75)
            beatPosition < 0.75 -> {
                val tPhase = (beatPosition - 0.5) / 0.25 * PI
                baseline + 0.3 * sin(tPhase)
            }
            // Rest of cycle
            else -> baseline
        }
        
        // Add small amount of noise
        val noise = random.nextGaussian() * 0.02
        return ecgValue + noise
    }
    
    /**
     * Generate PPG waveform data
     */
    private fun generatePPGData(elapsedTimeMs: Long, samplesNeeded: Int): DoubleArray {
        val data = DoubleArray(samplesNeeded)
        val normalPattern = template.normalPattern
        val heartRate = normalPattern?.heartRate ?: 75
        val systolicPeak = normalPattern?.systolicPeak ?: 2048
        val diastolicTrough = normalPattern?.diastolicTrough ?: 1024
        val baseline = normalPattern?.baseline ?: 1536
        
        val beatsPerSecond = heartRate / 60.0
        val samplesPerBeat = template.sampleRate / beatsPerSecond
        
        for (i in 0 until samplesNeeded) {
            val beatPosition = (sampleIndex % samplesPerBeat.toInt()) / samplesPerBeat
            data[i] = generatePPGSample(beatPosition, systolicPeak, diastolicTrough, baseline)
            sampleIndex++
        }
        
        return data
    }
    
    /**
     * Generate a single PPG sample
     */
    private fun generatePPGSample(beatPosition: Double, systolicPeak: Int, diastolicTrough: Int, baseline: Int): Double {
        // PPG waveform: sharp rise, gradual fall with dicrotic notch
        val ppgValue = when {
            // Systolic upstroke (0.0 - 0.2)
            beatPosition < 0.2 -> {
                val phase = beatPosition / 0.2
                baseline + (systolicPeak - baseline) * (1 - cos(phase * PI / 2))
            }
            // Systolic peak and early diastole (0.2 - 0.4)
            beatPosition < 0.4 -> {
                val phase = (beatPosition - 0.2) / 0.2
                systolicPeak - (systolicPeak - baseline * 0.8) * phase
            }
            // Dicrotic notch (0.4 - 0.5)
            beatPosition < 0.5 -> {
                val phase = (beatPosition - 0.4) / 0.1
                baseline * 0.8 - 50 * sin(phase * PI)
            }
            // Late diastole (0.5 - 1.0)
            else -> {
                val phase = (beatPosition - 0.5) / 0.5
                (baseline * 0.8) - ((baseline * 0.8) - diastolicTrough) * phase
            }
        }
        
        // Add noise
        val noise = random.nextGaussian() * 10
        return ppgValue + noise
    }
    
    /**
     * Generate respiratory waveform data
     */
    private fun generateRESPData(elapsedTimeMs: Long, samplesNeeded: Int): DoubleArray {
        val data = DoubleArray(samplesNeeded)
        val normalPattern = template.normalPattern
        val respiratoryRate = normalPattern?.respiratoryRate ?: 16
        val amplitude = normalPattern?.amplitude ?: 50
        val baseline = normalPattern?.baseline ?: 1000
        
        val breathsPerSecond = respiratoryRate / 60.0
        val samplesPerBreath = template.sampleRate / breathsPerSecond
        
        for (i in 0 until samplesNeeded) {
            val breathPosition = (sampleIndex % samplesPerBreath.toInt()) / samplesPerBreath
            data[i] = generateRESPSample(breathPosition, amplitude, baseline)
            sampleIndex++
        }
        
        return data
    }
    
    /**
     * Generate a single respiratory sample
     */
    private fun generateRESPSample(breathPosition: Double, amplitude: Int, baseline: Int): Double {
        // Respiratory waveform: sinusoidal with slight asymmetry
        val inspirationPhase = 0.4 // 40% inspiration, 60% expiration
        
        val respValue = if (breathPosition < inspirationPhase) {
            // Inspiration - steeper rise
            val phase = breathPosition / inspirationPhase * PI / 2
            baseline + amplitude * sin(phase)
        } else {
            // Expiration - gradual fall
            val phase = (breathPosition - inspirationPhase) / (1 - inspirationPhase) * PI / 2
            baseline + amplitude * cos(phase)
        }
        
        // Add noise
        val noise = random.nextGaussian() * 5
        return respValue + noise
    }
    
    /**
     * Generate generic waveform from sample data
     */
    private fun generateGenericWaveform(samplesNeeded: Int): DoubleArray {
        val data = DoubleArray(samplesNeeded)
        
        if (baseSampleData.isEmpty()) {
            // Generate simple sine wave if no sample data
            for (i in 0 until samplesNeeded) {
                data[i] = sin(2 * PI * i / 100.0) + random.nextGaussian() * 0.1
            }
        } else {
            // Use sample data with interpolation
            for (i in 0 until samplesNeeded) {
                val index = sampleIndex % baseSampleData.size
                data[i] = baseSampleData[index] + random.nextGaussian() * 0.1
                sampleIndex++
            }
        }
        
        return data
    }
    
    /**
     * Get sample rate for this waveform
     */
    fun getSampleRate(): Int = template.sampleRate
    
    /**
     * Get waveform type
     */
    fun getWaveformType(): String = waveformType
    
    /**
     * Reset generator state
     */
    fun reset() {
        sampleIndex = 0
        heartBeatPhase = 0.0
        respiratoryPhase = 0.0
        lastSampleTime = 0L
    }
    
    /**
     * Generate alarm pattern waveform
     */
    fun generateAlarmPattern(patternType: String, samplesNeeded: Int): DoubleArray {
        return when (patternType.lowercase()) {
            "flatline" -> DoubleArray(samplesNeeded) { 0.0 }
            "chaotic" -> DoubleArray(samplesNeeded) { random.nextGaussian() * 0.5 }
            "noise" -> DoubleArray(samplesNeeded) { random.nextGaussian() * 0.1 }
            else -> generateGenericWaveform(samplesNeeded)
        }
    }
}
