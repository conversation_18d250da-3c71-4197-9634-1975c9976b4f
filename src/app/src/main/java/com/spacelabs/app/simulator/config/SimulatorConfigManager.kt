package com.spacelabs.app.simulator.config

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.spacelabs.app.common.LogWriter

/**
 * Configuration manager for the medical data simulator
 * Handles simulator settings, preferences, and state persistence
 */
class SimulatorConfigManager(private val context: Context) {

    companion object {
        private const val TAG = "SimulatorConfigManager"
        private const val PREFS_NAME = "medical_simulator_prefs"
        
        // Preference keys
        private const val KEY_ENABLED = "simulator_enabled"
        private const val KEY_CURRENT_PATIENT_SCENARIO = "current_patient_scenario"
        private const val KEY_CURRENT_ALARM_SCENARIO = "current_alarm_scenario"
        private const val KEY_UPDATE_INTERVAL = "update_interval_ms"
        private const val KEY_AUTO_START = "auto_start_simulation"
        private const val KEY_NOISE_LEVEL = "noise_level"
        private const val KEY_VARIATION_INTENSITY = "variation_intensity"
        
        // Default values
        private const val DEFAULT_UPDATE_INTERVAL = 1000L
        private const val DEFAULT_NOISE_LEVEL = 0.1f
        private const val DEFAULT_VARIATION_INTENSITY = 1.0f
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    private val dataLoader = MedicalDataLoader(context)

    /**
     * Check if simulator is enabled
     */
    fun isSimulatorEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_ENABLED, false)
    }

    /**
     * Enable or disable simulator
     */
    fun setSimulatorEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_ENABLED, enabled)
            .apply()
        Log.d(TAG, "Simulator enabled: $enabled")
    }

    /**
     * Get current patient scenario ID
     */
    fun getCurrentPatientScenario(): String? {
        return sharedPreferences.getString(KEY_CURRENT_PATIENT_SCENARIO, null)
    }

    /**
     * Set current patient scenario
     */
    fun setCurrentPatientScenario(scenarioId: String?) {
        sharedPreferences.edit()
            .putString(KEY_CURRENT_PATIENT_SCENARIO, scenarioId)
            .apply()
        Log.d(TAG, "Current patient scenario: $scenarioId")
    }

    /**
     * Get current alarm scenario ID
     */
    fun getCurrentAlarmScenario(): String? {
        return sharedPreferences.getString(KEY_CURRENT_ALARM_SCENARIO, null)
    }

    /**
     * Set current alarm scenario
     */
    fun setCurrentAlarmScenario(scenarioId: String?) {
        sharedPreferences.edit()
            .putString(KEY_CURRENT_ALARM_SCENARIO, scenarioId)
            .apply()
        Log.d(TAG, "Current alarm scenario: $scenarioId")
    }

    /**
     * Get update interval in milliseconds
     */
    fun getUpdateInterval(): Long {
        return sharedPreferences.getLong(KEY_UPDATE_INTERVAL, DEFAULT_UPDATE_INTERVAL)
    }

    /**
     * Set update interval in milliseconds
     */
    fun setUpdateInterval(intervalMs: Long) {
        sharedPreferences.edit()
            .putLong(KEY_UPDATE_INTERVAL, intervalMs)
            .apply()
        Log.d(TAG, "Update interval: ${intervalMs}ms")
    }

    /**
     * Check if auto-start is enabled
     */
    fun isAutoStartEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_AUTO_START, false)
    }

    /**
     * Enable or disable auto-start
     */
    fun setAutoStartEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_AUTO_START, enabled)
            .apply()
        Log.d(TAG, "Auto-start enabled: $enabled")
    }

    /**
     * Get noise level (0.0 - 1.0)
     */
    fun getNoiseLevel(): Float {
        return sharedPreferences.getFloat(KEY_NOISE_LEVEL, DEFAULT_NOISE_LEVEL)
    }

    /**
     * Set noise level (0.0 - 1.0)
     */
    fun setNoiseLevel(level: Float) {
        val clampedLevel = level.coerceIn(0.0f, 1.0f)
        sharedPreferences.edit()
            .putFloat(KEY_NOISE_LEVEL, clampedLevel)
            .apply()
        Log.d(TAG, "Noise level: $clampedLevel")
    }

    /**
     * Get variation intensity (0.0 - 2.0)
     */
    fun getVariationIntensity(): Float {
        return sharedPreferences.getFloat(KEY_VARIATION_INTENSITY, DEFAULT_VARIATION_INTENSITY)
    }

    /**
     * Set variation intensity (0.0 - 2.0)
     */
    fun setVariationIntensity(intensity: Float) {
        val clampedIntensity = intensity.coerceIn(0.0f, 2.0f)
        sharedPreferences.edit()
            .putFloat(KEY_VARIATION_INTENSITY, clampedIntensity)
            .apply()
        Log.d(TAG, "Variation intensity: $clampedIntensity")
    }

    /**
     * Get available patient scenarios with names
     */
    fun getAvailablePatientScenariosWithNames(): List<Pair<String, String>> {
        return try {
            val templates = dataLoader.loadMedicalDataTemplates()
            templates?.patientScenarios?.map { scenario ->
                Pair(scenario.scenarioId, scenario.name)
            } ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error loading patient scenarios: ${e.message}")
            LogWriter.writeExceptLog("SimulatorConfigManager", "Error loading scenarios: ${e.stackTraceToString()}")
            emptyList()
        }
    }

    /**
     * Get available alarm scenarios with names
     */
    fun getAvailableAlarmScenariosWithNames(): List<Pair<String, String>> {
        return try {
            val scenarios = dataLoader.loadAlarmScenarios()
            scenarios?.alarmScenarios?.map { scenario ->
                Pair(scenario.scenarioId, scenario.name)
            } ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error loading alarm scenarios: ${e.message}")
            LogWriter.writeExceptLog("SimulatorConfigManager", "Error loading alarm scenarios: ${e.stackTraceToString()}")
            emptyList()
        }
    }

    /**
     * Get patient scenario details
     */
    fun getPatientScenarioDetails(scenarioId: String): PatientScenario? {
        return try {
            dataLoader.loadPatientScenario(scenarioId)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading patient scenario details: ${e.message}")
            null
        }
    }

    /**
     * Get alarm scenario details
     */
    fun getAlarmScenarioDetails(scenarioId: String): AlarmScenario? {
        return try {
            dataLoader.loadAlarmScenario(scenarioId)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading alarm scenario details: ${e.message}")
            null
        }
    }

    /**
     * Validate current configuration
     */
    fun validateConfiguration(): ConfigValidationResult {
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        // Check if patient scenario exists
        val patientScenario = getCurrentPatientScenario()
        if (patientScenario != null) {
            val scenario = getPatientScenarioDetails(patientScenario)
            if (scenario == null) {
                errors.add("Patient scenario '$patientScenario' not found")
            }
        } else if (isSimulatorEnabled()) {
            warnings.add("No patient scenario selected")
        }

        // Check if alarm scenario exists (if set)
        val alarmScenario = getCurrentAlarmScenario()
        if (alarmScenario != null) {
            val scenario = getAlarmScenarioDetails(alarmScenario)
            if (scenario == null) {
                errors.add("Alarm scenario '$alarmScenario' not found")
            }
        }

        // Check update interval
        val updateInterval = getUpdateInterval()
        if (updateInterval < 100) {
            warnings.add("Update interval is very short (${updateInterval}ms), may impact performance")
        } else if (updateInterval > 10000) {
            warnings.add("Update interval is very long (${updateInterval}ms), data may appear choppy")
        }

        return ConfigValidationResult(
            isValid = errors.isEmpty(),
            errors = errors,
            warnings = warnings
        )
    }

    /**
     * Reset configuration to defaults
     */
    fun resetToDefaults() {
        sharedPreferences.edit()
            .clear()
            .putLong(KEY_UPDATE_INTERVAL, DEFAULT_UPDATE_INTERVAL)
            .putFloat(KEY_NOISE_LEVEL, DEFAULT_NOISE_LEVEL)
            .putFloat(KEY_VARIATION_INTENSITY, DEFAULT_VARIATION_INTENSITY)
            .apply()
        Log.d(TAG, "Configuration reset to defaults")
    }

    /**
     * Export configuration as JSON string
     */
    fun exportConfiguration(): String {
        val config = mapOf(
            "enabled" to isSimulatorEnabled(),
            "patientScenario" to getCurrentPatientScenario(),
            "alarmScenario" to getCurrentAlarmScenario(),
            "updateInterval" to getUpdateInterval(),
            "autoStart" to isAutoStartEnabled(),
            "noiseLevel" to getNoiseLevel(),
            "variationIntensity" to getVariationIntensity()
        )
        
        return try {
            // Simple JSON serialization - in production, use Gson
            config.entries.joinToString(",", "{", "}") { (key, value) ->
                "\"$key\":${if (value is String) "\"$value\"" else value}"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error exporting configuration: ${e.message}")
            "{}"
        }
    }

    /**
     * Get simulator configuration summary
     */
    fun getConfigurationSummary(): SimulatorConfigSummary {
        return SimulatorConfigSummary(
            isEnabled = isSimulatorEnabled(),
            patientScenario = getCurrentPatientScenario(),
            alarmScenario = getCurrentAlarmScenario(),
            updateIntervalMs = getUpdateInterval(),
            autoStart = isAutoStartEnabled(),
            noiseLevel = getNoiseLevel(),
            variationIntensity = getVariationIntensity(),
            validationResult = validateConfiguration()
        )
    }
}

/**
 * Configuration validation result
 */
data class ConfigValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String>
)

/**
 * Simulator configuration summary
 */
data class SimulatorConfigSummary(
    val isEnabled: Boolean,
    val patientScenario: String?,
    val alarmScenario: String?,
    val updateIntervalMs: Long,
    val autoStart: Boolean,
    val noiseLevel: Float,
    val variationIntensity: Float,
    val validationResult: ConfigValidationResult
)
