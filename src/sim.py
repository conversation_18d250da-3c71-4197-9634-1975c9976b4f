import asyncio
import websockets
import json
import time

# List of JSON messages to cycle through
messages = [
    {"information":{"facilityId":"UnifiedGateway","groupName":"1","deviceId":"BED-001","bedId":"538181904","alarmMode":0,"status":{"admitted":1,"connected":1,"comfortCare":0,"pairingSAI":0,"pairingV2":0,"pairing":0,"transferring":0,"measuring":1},"patientDOB":"","timeStamp":1751278326192,"patientId":"NK8110","patientName":"PRAKASH","sequenceNumber":1648,"packetNumber":1,"totalPackets":1},"VS":[{"name":"HR","value":"80","priority":"Warning"},{"name":"VPC/m","value":"0"},{"name":"ST II","value":"+0.02"},{"name":"Apnea","value":"1"},{"name":"RR/Imp","value":"12"},{"name":"IMP/RR","value":"12"},{"name":"IMP/Apnea","value":"1"},{"name":"SpO2","value":"98"},{"name":"SpO2/PR","value":"80"},{"name":"SpO2/PI","value":"1.40"},{"name":"SpO2/SQI","value":"4"},{"name":"NIBP-S","value":"100","timeTaken":"20250630100459"},{"name":"NIBP-D","value":"50","timeTaken":"20250630100459"},{"name":"NIBP-M","value":"75","timeTaken":"20250630100459"},{"name":"Temp","value":"36.5"},{"name":"PR/SpO2","value":"80"}],"arrhythmia":{"alarm":"Brady","priority":"Warning"}},
    {"information":{"facilityId":"UnifiedGateway","groupName":"1","deviceId":"BED-001","bedId":"538181904","alarmMode":0,"status":{"admitted":1,"connected":1,"comfortCare":0,"pairingSAI":0,"pairingV2":0,"pairing":0,"transferring":0,"measuring":1},"patientDOB":"","timeStamp":1751278383481,"patientId":"NK8110","patientName":"PRAKASH","sequenceNumber":1872,"packetNumber":1,"totalPackets":1},"VS":[{"name":"HR","value":"80","priority":"Warning"},{"name":"VPC/m","value":"0"},{"name":"ST II","value":"+0.02"},{"name":"Apnea","value":"4"},{"name":"RR/Imp","value":"12"},{"name":"IMP/RR","value":"12"},{"name":"IMP/Apnea","value":"4"},{"name":"SpO2","value":"98"},{"name":"SpO2/PR","value":"80"},{"name":"SpO2/PI","value":"1.40"},{"name":"SpO2/SQI","value":"4"},{"name":"NIBP-S","value":"100","timeTaken":"20250630100459"},{"name":"NIBP-D","value":"50","timeTaken":"20250630100459"},{"name":"NIBP-M","value":"75","timeTaken":"20250630100459"},{"name":"Temp","value":"36.5"},{"name":"PR/SpO2","value":"80"}],"arrhythmia":{"alarm":"Tachy","priority":"Warning"}},
    {"information":{"facilityId":"UnifiedGateway","groupName":"1","deviceId":"BED-001","bedId":"538181904","alarmMode":0,"status":{"admitted":1,"connected":1,"comfortCare":0,"pairingSAI":0,"pairingV2":0,"pairing":0,"transferring":0,"measuring":1},"patientDOB":"","timeStamp":1751278825862,"patientId":"NK8110","patientName":"PRAKASH","sequenceNumber":3600,"packetNumber":1,"totalPackets":1},"VS":[{"name":"HR","value":"0","priority":"Warning"},{"name":"VPC/m","value":"0"},{"name":"ST II","value":"---"},{"name":"ST aVL","value":"---"},{"name":"Apnea","value":"2"},{"name":"RR/Imp","value":"12"},{"name":"IMP/RR","value":"12"},{"name":"IMP/Apnea","value":"2"},{"name":"SpO2","value":"98","priority":"Warning"},{"name":"SpO2/PR","value":"80"},{"name":"SpO2/PI","value":"1.40"},{"name":"SpO2/SQI","value":"4"},{"name":"NIBP-S","value":"100","timeTaken":"20250630100459"},{"name":"NIBP-D","value":"50","timeTaken":"20250630100459"},{"name":"NIBP-M","value":"75","timeTaken":"20250630100459"},{"name":"Temp","value":"36.5"},{"name":"PR/SpO2","value":"80"}],"arrhythmia":{"alarm":"Asystole","priority":"Crisis"}},
   {"information":{"facilityId":"UnifiedGateway","groupName":"1","deviceId":"BED-001","bedId":"538181904","alarmMode":0,"status":{"admitted":1,"connected":1,"comfortCare":0,"pairingSAI":0,"pairingV2":0,"pairing":0,"transferring":0,"measuring":1},"patientDOB":"","timeStamp":1751279355276,"patientId":"NK8110","patientName":"PRAKASH","sequenceNumber":5668,"packetNumber":1,"totalPackets":1},"VS":[{"name":"HR","value":"80","priority":"Crisis"},{"name":"VPC/m","value":"0"},{"name":"ST II","value":"+0.02"},{"name":"ST aVF","value":"+0.02","priority":"Warning"},{"name":"Apnea","value":"3"},{"name":"RR/Imp","value":"12"},{"name":"IMP/RR","value":"12"},{"name":"IMP/Apnea","value":"3"},{"name":"SpO2","value":"98","priority":"Warning"},{"name":"SpO2/PR","value":"80"},{"name":"SpO2/PI","value":"1.40"},{"name":"SpO2/SQI","value":"4"},{"name":"NIBP-S","value":"100","timeTaken":"20250630100459"},{"name":"NIBP-D","value":"50","timeTaken":"20250630100459"},{"name":"NIBP-M","value":"75","timeTaken":"20250630100459"},{"name":"Temp","value":"36.5"},{"name":"PR/SpO2","value":"80"}],"arrhythmia":{"alarm":"Brady","priority":"Crisis"}},
   {"information":{"facilityId":"UnifiedGateway","groupName":"1","deviceId":"BED-001","bedId":"538181904","alarmMode":0,"status":{"admitted":1,"connected":1,"comfortCare":0,"pairingSAI":0,"pairingV2":0,"pairing":0,"transferring":0,"measuring":1},"patientDOB":"","timeStamp":1751279418763,"patientId":"NK8110","patientName":"PRAKASH","sequenceNumber":5916,"packetNumber":1,"totalPackets":1},"VS":[{"name":"HR","value":"80","priority":"Advisory"},{"name":"VPC/m","value":"0"},{"name":"ST II","value":"+0.02"},{"name":"ST aVF","value":"+0.02","priority":"Warning"},{"name":"Apnea","value":"1"},{"name":"RR/Imp","value":"12"},{"name":"IMP/RR","value":"12"},{"name":"IMP/Apnea","value":"1"},{"name":"SpO2","value":"98","priority":"Warning"},{"name":"SpO2/PR","value":"80"},{"name":"SpO2/PI","value":"1.40"},{"name":"SpO2/SQI","value":"4"},{"name":"NIBP-S","value":"100","timeTaken":"20250630100459"},{"name":"NIBP-D","value":"50","timeTaken":"20250630100459"},{"name":"NIBP-M","value":"75","timeTaken":"20250630100459"},{"name":"Temp","value":"36.5"},{"name":"PR/SpO2","value":"80"}],"arrhythmia":{"alarm":"Brady","priority":"Advisory"}},
   #{"information":{"deviceId":"BED-001","patientId":"NK1880","facilityId":"UnifiedGatew","timeStart":#1748320141567,"sequenceStart":2868},"waveform":{"name":"RIMP","sampleRate":#8,"data":"461,460,458,456,454,452,450,448,446,444,442,440,437,436,433,430,427,426,423,420,418,415,413,410,407,405,402,400,397,393,391,388,386,382,380,377,374,371,368,365,361,358,354,350,347,342,338,334,329,325,321,315,311,306,300,295,289,282,276,270,263,256,249,241,234,226,218,210,202,193,184,174,166,156,147,137,126,117,106,97,87,76,67,57,47,37,27,18,8,-1,-9,-14,-16,-17,-16,-17,-17,-16,-16,-15,-16,-15,-14,-14,-14,-13,-13,-12,-12,-11,-12,-11,-10,-10,-10,-9,-10,-9,-8,-8,-7,-8,-7,-7,-7,-6,-6,-5"}},
   #{"information":{"deviceId":"BED-001","patientId":"NK1880","facilityId":"UnifiedGatew","timeStart":#1748320141567,"sequenceStart":2868},"waveform":{"name":"II","sampleRate":#4,"data":"-13,-13,-13,-13,-13,-14,-14,-14,-10,0,21,50,85,121,148,161,155,128,92,51,13,-13,-29,-35,-35,-32,-29,-27,-24,-23,-22,-19,-17,-14,-12,-10,-9,-8,-8,-7,-7,-5,-5,-5,-5,-4,-4,-3,-3,-3,-3,-2,-2,-2,-2,-2,-2,-2,-2,-2,1,1,2,3,3,5,6,7,10,11,12,15,16,18,20,21,22,23,25,26,27,26,26,27,27,25,25,23,22,20,17,16,13,11,8,6,3,2,0,-3,-5,-8,-10,-12,-13,-13,-13,-14,-14,-14,-14,-14,-14,-13,-13,-15,-15,-15,-17,-17,-17,-17,-17,-17,-17,-15,-15,-15,-15,-17,-17,-17,-17,-17,-17,-17,-17,-15,-15,-15,-15,-14,-14,-14,-15,-15,-15,-15,-14,-14,-13,-13,-13,-13,-13,-14,-14,-13,-12,-10,-9,-7,-5,-3,-2,-2,0,0,0,0,0,-2,-3,-4,-5,-8,-9,-12,-13,-13,-14,-14,-14,-14,-14,-13,-13,-13,-13,-13,-13,-13,-13,-14,-14,-14,-10,0,21,50,85,121,148,161,155,128,92,51,13,-13,-29,-35,-35,-32,-29,-27,-24,-23,-22,-19,-17,-14,-12,-10,-9,-8,-8,-7,-7,-5,-5,-5,-5,-4,-4,-3,-3,-3,-3,-2,-2,-2,-2,-2,-2,-2,-2,-2,1,1,2,3,3,5,6,7"}},
  # {"information":{"deviceId":"BED-001","patientId":"NK1880","facilityId":"UnifiedGatew","timeStart":#1748320141567,"sequenceStart":2868},"waveform":{"name":"SpO2","sampleRate":#8,"data":"852,804,740,672,592,512,420,324,216,112,-8,-128,-252,-380,-508,-636,-760,-888,-1004,-1124,-1236,-1352,-1456,-1560,-1656,-1756,-1844,-1936,-2020,-2108,-2184,-2264,-2340,-2416,-2480,-2548,-2596,-2648,-2668,-2688,-2664,-2640,-2556,-2472,-2324,-2176,-1960,-1744,-1472,-1200,-888,-576,-248,80,396,712,992,1272,1500,1724,1888,2048,2148,2248,2296,2340,2344,2344,2312,2284,2232,2176,2112,2044,1968,1892,1812,1732,1656,1576,1504,1432,1372,1308,1256,1204,1164,1120,1084,1052,1016,984,944,904,852,804,740,672,592,512,420,324,216,112,-8,-128,-252,-380,-508,-636,-760,-888,-1004,-1124,-1236,-1352,-1456,-1560,-1656,-1756,-1844,-1936,-2020,-2108,-2184,-2264,-2340,-2416"}}

]

SEND_INTERVAL = 1  # seconds

# Track connected clients
connected_clients = set()

async def send_messages(websocket):
    print(f"Client connected: {websocket.remote_address}")
    connected_clients.add(websocket)
    try:
        while True:
            for message in messages:
                message["information"]["timeStamp"] = int(time.time() * 1000)
                message["information"]["sequenceNumber"] += 1
                await websocket.send(json.dumps(message))
                print("Sent:", message["information"]["sequenceNumber"])
                await asyncio.sleep(SEND_INTERVAL)
    except websockets.exceptions.ConnectionClosed:
        print(f"Client disconnected: {websocket.remote_address}")
    finally:
        connected_clients.remove(websocket)

async def main():
    print("Starting WebSocket server on ws://0.0.0.0:8071")
    async with websockets.serve(send_messages, "0.0.0.0", 8071):
        await asyncio.Future()  # run forever

asyncio.run(main())

